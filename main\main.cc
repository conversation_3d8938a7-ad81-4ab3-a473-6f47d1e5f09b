#include <esp_log.h>
#include <esp_err.h>
#include <nvs.h>
#include <nvs_flash.h>
#include <driver/gpio.h>
#include <esp_event.h>

#include "application.h"
#include "system_info.h"
#include "link_protocol.h"

#define TAG "main"
#include <stdio.h>
#include <vector>
#include <string>
#include <mutex>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_event.h"
#include "nvs_flash.h"
#include "esp_log.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "services/gap/ble_svc_gap.h"
#include "sdkconfig.h"
#include <cJSON.h>

uint8_t ble_addr_type;



extern "C" void app_main(void)
{
    // Initialize the default event loop
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    // Initialize NVS flash for WiFi configuration
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_LOGW(TAG, "Erasing NVS flash to fix corruption");
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    nimble_port_init();                           
    // Device name will be set in advertising data instead
    ble_hs_cfg.sync_cb = ble_app_on_sync;           // 4 - Set application
    nimble_port_freertos_init(host_task);           // 5 - Set infinite task
    // Start link cycle task
    //ble_setname("Link");
    xTaskCreate(link_cycle_task, "link_cycle", 2048, NULL, 5, NULL);
    ESP_LOGI(TAG, "Link cycle task started");
    // Launch the application
    auto& app = Application::GetInstance();
    app.Start();


    app.MainEventLoop();
}
