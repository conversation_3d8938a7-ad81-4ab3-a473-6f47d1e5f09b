#!/usr/bin/env python3
"""
LinkCID BLE broadcasting test script
Based on link_protocol.cc implementation

This script implements real BLE broadcasting for LinkCID using:
- Manufacturer ID: 0xFFFF
- BLE Extended Advertising
- Manufacturer data format: [0xFF, 0xFF, LinkCID_string...]
"""

import struct
import time
import random
import subprocess
import sys

try:
    from bleak import BleakScanner
    from bleak.backends.bluezdbus.advertisement_monitor import OrPattern
    import asyncio
    BLEAK_AVAILABLE = True
except ImportError:
    BLEAK_AVAILABLE = False
    print("Warning: bleak not available. Install with: pip install bleak")

try:
    import bluetooth._bluetooth as bluez
    import bluetooth
    PYBLUEZ_AVAILABLE = True
except ImportError:
    PYBLUEZ_AVAILABLE = False
    print("Warning: pybluez not available. Install with: pip install pybluez")


def broad_linkcid(linkcid):
    """
    Real BLE LinkCID broadcasting functionality from link_protocol.cc

    Args:
        linkcid (str): The LinkCID string to broadcast

    Returns:
        dict: Result of the broadcast operation
    """

    # Constants from link_protocol.cc
    MANUFACTURER_ID = 0xFFFF  # LinkCID broadcast uses 0xFFFF
    MAX_LINKCID_LENGTH = 1600
    BROADCAST_DURATION_MS = 200
    RANDOM_DELAY_MAX_MS = 20

    print(f"[BROADCAST] Starting real BLE LinkCID broadcast: {linkcid}")

    # Validate LinkCID (mirrors validation logic from link_protocol.cc)
    if not linkcid:
        return {"success": False, "message": "LinkCID cannot be empty"}

    if len(linkcid) > MAX_LINKCID_LENGTH:
        return {"success": False, "message": f"LinkCID too long: {len(linkcid)} bytes (max {MAX_LINKCID_LENGTH})"}

    # Check for valid IPFS hash format (Qm... or baf...)
    if len(linkcid) < 10 or not (linkcid.startswith("Qm") or linkcid.startswith("baf")):
        return {"success": False, "message": "Invalid LinkCID format (must start with 'Qm' or 'baf' and be at least 10 chars)"}

    # Try different BLE broadcasting methods
    try:
        # Method 1: Try using bluetoothctl command
        result = broadcast_with_bluetoothctl(linkcid, MANUFACTURER_ID, BROADCAST_DURATION_MS, RANDOM_DELAY_MAX_MS)
        if result["success"]:
            return result

        # Method 2: Try using hcitool (if available)
        result = broadcast_with_hcitool(linkcid, MANUFACTURER_ID, BROADCAST_DURATION_MS, RANDOM_DELAY_MAX_MS)
        if result["success"]:
            return result

        # Method 3: Try using Python bluetooth libraries
        if PYBLUEZ_AVAILABLE:
            result = broadcast_with_pybluez(linkcid, MANUFACTURER_ID, BROADCAST_DURATION_MS, RANDOM_DELAY_MAX_MS)
            if result["success"]:
                return result

        return {"success": False, "message": "No available BLE broadcasting method found"}

    except Exception as e:
        return {"success": False, "message": f"Broadcast failed: {str(e)}"}


def broadcast_with_bluetoothctl(linkcid, manufacturer_id, duration_ms, random_delay_max):
    """Broadcast using bluetoothctl command"""
    try:
        print("[BROADCAST] Trying bluetoothctl method...")

        # Create advertising data
        linkcid_bytes = linkcid.encode('utf-8')

        # Build manufacturer data: Company ID (little-endian) + LinkCID
        mfg_data = struct.pack('<H', manufacturer_id) + linkcid_bytes
        mfg_data_hex = mfg_data.hex()

        # Random delay
        random_delay = random.randint(0, random_delay_max)
        total_duration = duration_ms + random_delay

        print(f"[BROADCAST] Manufacturer data: {mfg_data_hex}")
        print(f"[BROADCAST] Broadcasting for {total_duration}ms")

        # Start advertising with bluetoothctl
        commands = [
            "power on",
            "discoverable on",
            f"advertise manufacturer {manufacturer_id:04x} {linkcid_bytes.hex()}",
            "advertise on"
        ]

        start_time = time.time()

        for cmd in commands:
            result = subprocess.run(
                ["bluetoothctl", "--", cmd],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode != 0:
                print(f"[BROADCAST] Command failed: {cmd}")
                print(f"[BROADCAST] Error: {result.stderr}")

        # Wait for broadcast duration
        time.sleep(total_duration / 1000.0)

        # Stop advertising
        subprocess.run(["bluetoothctl", "--", "advertise", "off"], capture_output=True, timeout=5)

        end_time = time.time()
        actual_duration = int((end_time - start_time) * 1000)

        return {
            "success": True,
            "message": "LinkCID broadcast completed with bluetoothctl",
            "method": "bluetoothctl",
            "linkcid": linkcid,
            "duration_ms": actual_duration,
            "manufacturer_id": f"0x{manufacturer_id:04X}"
        }

    except subprocess.TimeoutExpired:
        return {"success": False, "message": "bluetoothctl command timeout"}
    except FileNotFoundError:
        return {"success": False, "message": "bluetoothctl not found"}
    except Exception as e:
        return {"success": False, "message": f"bluetoothctl error: {str(e)}"}


def broadcast_with_hcitool(linkcid, manufacturer_id, duration_ms, random_delay_max):
    """Broadcast using hcitool command"""
    try:
        print("[BROADCAST] Trying hcitool method...")

        # Create advertising data
        linkcid_bytes = linkcid.encode('utf-8')

        # Build complete advertising packet
        # Flags: 02 01 06 (General Discoverable + BR/EDR Not Supported)
        flags = "020106"

        # Manufacturer data: Length + Type(FF) + Company ID + Data
        mfg_data = struct.pack('<H', manufacturer_id) + linkcid_bytes
        mfg_length = len(mfg_data) + 1  # +1 for type byte
        mfg_packet = f"{mfg_length:02x}ff{mfg_data.hex()}"

        # Complete advertising data
        adv_data = flags + mfg_packet

        # Random delay
        random_delay = random.randint(0, random_delay_max)
        total_duration = duration_ms + random_delay

        print(f"[BROADCAST] Advertising data: {adv_data}")
        print(f"[BROADCAST] Broadcasting for {total_duration}ms")

        start_time = time.time()

        # Enable advertising
        subprocess.run(["hciconfig", "hci0", "up"], check=True, capture_output=True)
        subprocess.run(["hciconfig", "hci0", "leadv", "3"], check=True, capture_output=True)

        # Set advertising data
        subprocess.run(["hcitool", "-i", "hci0", "cmd", "0x08", "0x0008"] +
                      [adv_data[i:i+2] for i in range(0, len(adv_data), 2)],
                      check=True, capture_output=True)

        # Wait for broadcast duration
        time.sleep(total_duration / 1000.0)

        # Disable advertising
        subprocess.run(["hciconfig", "hci0", "noleadv"], capture_output=True)

        end_time = time.time()
        actual_duration = int((end_time - start_time) * 1000)

        return {
            "success": True,
            "message": "LinkCID broadcast completed with hcitool",
            "method": "hcitool",
            "linkcid": linkcid,
            "duration_ms": actual_duration,
            "manufacturer_id": f"0x{manufacturer_id:04X}"
        }

    except subprocess.CalledProcessError as e:
        return {"success": False, "message": f"hcitool command failed: {e}"}
    except FileNotFoundError:
        return {"success": False, "message": "hcitool not found"}
    except Exception as e:
        return {"success": False, "message": f"hcitool error: {str(e)}"}


def broadcast_with_pybluez(linkcid, manufacturer_id, duration_ms, random_delay_max):
    """Broadcast using PyBluez library"""
    try:
        print("[BROADCAST] Trying PyBluez method...")

        # This is a placeholder - PyBluez doesn't directly support BLE advertising
        # Would need to use lower-level HCI commands
        return {"success": False, "message": "PyBluez BLE advertising not implemented"}

    except Exception as e:
        return {"success": False, "message": f"PyBluez error: {str(e)}"}


def board_linkcid_forever(linkcid):
    """
    Continuously broadcast LinkCID forever (until interrupted)
    Mirrors the link_cycle_task behavior from link_protocol.cc

    Args:
        linkcid (str): The LinkCID string to broadcast continuously

    Returns:
        dict: Result of the broadcast operation
    """

    # Constants from link_protocol.cc
    MANUFACTURER_ID = 0xFFFF
    BROADCAST_DURATION_MS = 200
    SCAN_DURATION_MS = 200
    SLEEP_DURATION_MS = 200
    RANDOM_DELAY_MAX_MS = 20

    print(f"[FOREVER] Starting continuous LinkCID broadcast: {linkcid}")
    print("[FOREVER] Press Ctrl+C to stop...")

    # Validate LinkCID first
    if not linkcid:
        return {"success": False, "message": "LinkCID cannot be empty"}

    if len(linkcid) > 1600:
        return {"success": False, "message": f"LinkCID too long: {len(linkcid)} bytes (max 1600)"}

    if len(linkcid) < 10 or not (linkcid.startswith("Qm") or linkcid.startswith("baf")):
        return {"success": False, "message": "Invalid LinkCID format"}

    cycle_count = 0
    start_time = time.time()

    try:
        while True:
            cycle_count += 1
            cycle_start = time.time()

            print(f"\n[FOREVER] === Cycle {cycle_count} ===")

            # Phase 1: Broadcast for 200ms + random delay (0-20ms)
            random_delay = random.randint(0, RANDOM_DELAY_MAX_MS)
            broadcast_duration = BROADCAST_DURATION_MS + random_delay

            print(f"[FOREVER] Phase 1: Broadcasting for {broadcast_duration}ms")
            broadcast_result = broadcast_single_cycle(linkcid, broadcast_duration)

            # Phase 2: Scan for 200ms (simulated - would need real BLE scanning)
            print(f"[FOREVER] Phase 2: Scanning for {SCAN_DURATION_MS}ms")
            time.sleep(SCAN_DURATION_MS / 1000.0)

            # Phase 3: Sleep for 200ms
            print(f"[FOREVER] Phase 3: Sleeping for {SLEEP_DURATION_MS}ms")
            time.sleep(SLEEP_DURATION_MS / 1000.0)

            cycle_end = time.time()
            cycle_duration = int((cycle_end - cycle_start) * 1000)
            total_runtime = int((cycle_end - start_time) * 1000)

            print(f"[FOREVER] Cycle {cycle_count} completed in {cycle_duration}ms (total runtime: {total_runtime}ms)")

    except KeyboardInterrupt:
        end_time = time.time()
        total_duration = int((end_time - start_time) * 1000)

        print(f"\n[FOREVER] Broadcast stopped by user")
        print(f"[FOREVER] Total cycles: {cycle_count}")
        print(f"[FOREVER] Total runtime: {total_duration}ms")

        return {
            "success": True,
            "message": "Continuous broadcast stopped by user",
            "linkcid": linkcid,
            "total_cycles": cycle_count,
            "total_duration_ms": total_duration
        }

    except Exception as e:
        return {"success": False, "message": f"Forever broadcast failed: {str(e)}"}


def board_linkcid_list(linkcid_list, cycles=1):
    """
    Broadcast a list of LinkCIDs in sequence
    Each LinkCID is broadcast for one complete cycle before moving to the next

    Args:
        linkcid_list (list): List of LinkCID strings to broadcast
        cycles (int): Number of complete cycles through the list (default: 1)

    Returns:
        dict: Result of the broadcast operation with details for each LinkCID
    """

    print(f"[LIST] Starting sequential LinkCID broadcast")
    print(f"[LIST] LinkCIDs: {len(linkcid_list)}, Cycles: {cycles}")

    if not linkcid_list:
        return {"success": False, "message": "LinkCID list cannot be empty"}

    if not isinstance(linkcid_list, list):
        return {"success": False, "message": "linkcid_list must be a list"}

    # Validate all LinkCIDs first
    for i, linkcid in enumerate(linkcid_list):
        if not linkcid:
            return {"success": False, "message": f"LinkCID at index {i} is empty"}

        if len(linkcid) > 1600:
            return {"success": False, "message": f"LinkCID at index {i} too long: {len(linkcid)} bytes"}

        if len(linkcid) < 10 or not (linkcid.startswith("Qm") or linkcid.startswith("baf")):
            return {"success": False, "message": f"Invalid LinkCID format at index {i}: {linkcid}"}

    results = []
    total_broadcasts = 0
    start_time = time.time()

    try:
        for cycle in range(cycles):
            print(f"\n[LIST] === Cycle {cycle + 1}/{cycles} ===")

            for i, linkcid in enumerate(linkcid_list):
                print(f"\n[LIST] Broadcasting LinkCID {i + 1}/{len(linkcid_list)}: {linkcid}")

                # Broadcast this LinkCID for one cycle (600ms total: 200ms broadcast + 200ms scan + 200ms sleep)
                cycle_start = time.time()

                # Phase 1: Broadcast
                random_delay = random.randint(0, 20)
                broadcast_duration = 200 + random_delay
                broadcast_result = broadcast_single_cycle(linkcid, broadcast_duration)

                # Phase 2: Scan (simulated)
                time.sleep(0.2)

                # Phase 3: Sleep
                time.sleep(0.2)

                cycle_end = time.time()
                cycle_duration = int((cycle_end - cycle_start) * 1000)

                result_entry = {
                    "linkcid": linkcid,
                    "cycle": cycle + 1,
                    "index": i,
                    "duration_ms": cycle_duration,
                    "broadcast_result": broadcast_result
                }

                results.append(result_entry)
                total_broadcasts += 1

                print(f"[LIST] LinkCID {i + 1} completed in {cycle_duration}ms")

        end_time = time.time()
        total_duration = int((end_time - start_time) * 1000)

        return {
            "success": True,
            "message": "Sequential LinkCID broadcast completed",
            "total_linkcids": len(linkcid_list),
            "total_cycles": cycles,
            "total_broadcasts": total_broadcasts,
            "total_duration_ms": total_duration,
            "results": results
        }

    except KeyboardInterrupt:
        end_time = time.time()
        total_duration = int((end_time - start_time) * 1000)

        print(f"\n[LIST] Broadcast stopped by user")

        return {
            "success": False,
            "message": "Sequential broadcast stopped by user",
            "total_broadcasts": total_broadcasts,
            "total_duration_ms": total_duration,
            "partial_results": results
        }

    except Exception as e:
        return {"success": False, "message": f"List broadcast failed: {str(e)}"}


def broadcast_single_cycle(linkcid, duration_ms):
    """
    Helper function to broadcast a single LinkCID for a specified duration

    Args:
        linkcid (str): LinkCID to broadcast
        duration_ms (int): Duration in milliseconds

    Returns:
        dict: Broadcast result
    """
    try:
        # Try bluetoothctl first
        result = broadcast_with_bluetoothctl(linkcid, 0xFFFF, duration_ms, 0)
        if result["success"]:
            return result

        # Try hcitool as fallback
        result = broadcast_with_hcitool(linkcid, 0xFFFF, duration_ms, 0)
        if result["success"]:
            return result

        # If no real broadcast available, simulate
        print(f"[CYCLE] Simulating broadcast for {duration_ms}ms")
        time.sleep(duration_ms / 1000.0)

        return {
            "success": True,
            "message": "Simulated broadcast completed",
            "method": "simulation",
            "linkcid": linkcid,
            "duration_ms": duration_ms
        }

    except Exception as e:
        return {"success": False, "message": f"Single cycle broadcast failed: {str(e)}"}


def extract_linkcid_from_advertising_data(adv_data):
    """
    Extract LinkCID from BLE advertising data
    Mirrors extract_linkcid_from_mfg_data function from link_protocol.cc

    Args:
        adv_data (bytes): BLE advertising data

    Returns:
        str: Extracted LinkCID or empty string if not found
    """
    MANUFACTURER_ID = 0xFFFF

    # Parse advertising data to find manufacturer data
    i = 0
    while i < len(adv_data):
        if i + 1 >= len(adv_data):
            break

        length = adv_data[i]
        if length == 0 or i + length + 1 > len(adv_data):
            break

        ad_type = adv_data[i + 1]

        # Look for manufacturer data (type 0xFF)
        if ad_type == 0xFF and length >= 3:
            # Extract manufacturer ID (little-endian)
            if i + 4 <= len(adv_data):
                mfg_id = struct.unpack('<H', adv_data[i + 2:i + 4])[0]

                # Check if it's LinkCID manufacturer ID
                if mfg_id == MANUFACTURER_ID:
                    # Extract LinkCID string
                    linkcid_data = adv_data[i + 4:i + 1 + length]
                    return linkcid_data.decode('utf-8', errors='ignore')

        i += length + 1

    return ""


# Test examples
if __name__ == '__main__':
    print("=== LinkCID Broadcast Test ===\n")

    # Test with default LinkCID from link_protocol.cc
    default_linkcid = "QmbfipMRwKAdB4V8dR4TTn9jYAuviLKWckDuEPtWFH6y9t"
    print("Test 1: Default LinkCID")
    result = broad_linkcid(default_linkcid)
    print(f"Result: {result}\n")