#!/usr/bin/env python3
"""
Simple test script for LinkCID broadcasting functionality
Based on link_protocol.cc implementation

This script simulates the LinkCID broadcast protocol which uses:
- Manufacturer ID: 0xFFFF
- BLE Extended Advertising
- Manufacturer data format: [0xFF, 0xFF, LinkCID_string...]
"""

import struct
import time
import random


def broad_linkcid(linkcid):
    """
    Simulate LinkCID broadcasting functionality from link_protocol.cc

    Args:
        linkcid (str): The LinkCID string to broadcast

    Returns:
        dict: Result of the broadcast operation
    """

    # Constants from link_protocol.cc
    MANUFACTURER_ID = 0xFFFF  # LinkCID broadcast uses 0xFFFF
    MAX_LINKCID_LENGTH = 1600
    BROADCAST_DURATION_MS = 200
    RANDOM_DELAY_MAX_MS = 20

    print(f"[BROADCAST] Starting LinkCID broadcast: {linkcid}")

    # Validate LinkCID (mirrors validation logic from link_protocol.cc)
    if not linkcid:
        return {"success": False, "message": "LinkCID cannot be empty"}

    if len(linkcid) > MAX_LINKCID_LENGTH:
        return {"success": False, "message": f"LinkCID too long: {len(linkcid)} bytes (max {MAX_LINKCID_LENGTH})"}

    # Check for valid IPFS hash format (Qm... or baf...)
    if len(linkcid) < 10 or not (linkcid.startswith("Qm") or linkcid.startswith("baf")):
        return {"success": False, "message": "Invalid LinkCID format (must start with 'Qm' or 'baf' and be at least 10 chars)"}

    # Create BLE advertising data (mirrors adv_linkcid function)
    try:
        # Build advertising packet structure
        # Flags (3 bytes): Length(1) + Type(1) + Flags(1)
        flags_data = bytes([0x02, 0x01, 0x06])  # General Discoverable + BR/EDR Not Supported

        # Manufacturer data: Length(1) + Type(1) + CompanyID(2) + LinkCID
        linkcid_bytes = linkcid.encode('utf-8')
        mfg_data_payload = struct.pack('<H', MANUFACTURER_ID) + linkcid_bytes  # Little-endian company ID + LinkCID
        mfg_header = bytes([len(mfg_data_payload) + 1, 0xFF])  # +1 for type byte

        # Complete advertising packet
        advertising_data = flags_data + mfg_header + mfg_data_payload

        print(f"[BROADCAST] Advertising data created: {len(advertising_data)} bytes")
        print(f"[BROADCAST] Manufacturer ID: 0x{MANUFACTURER_ID:04X}")
        print(f"[BROADCAST] LinkCID length: {len(linkcid_bytes)} bytes")

        # Simulate broadcast timing (mirrors link_cycle_task)
        random_delay = random.randint(0, RANDOM_DELAY_MAX_MS)
        broadcast_duration = BROADCAST_DURATION_MS + random_delay

        print(f"[BROADCAST] Broadcasting for {broadcast_duration}ms (base: {BROADCAST_DURATION_MS}ms + random: {random_delay}ms)")

        # Simulate the broadcast process
        start_time = time.time()
        time.sleep(broadcast_duration / 1000.0)  # Convert to seconds
        end_time = time.time()

        actual_duration = int((end_time - start_time) * 1000)
        print(f"[BROADCAST] Broadcast completed in {actual_duration}ms")

        # Verify the advertising data can be parsed back
        extracted_linkcid = extract_linkcid_from_advertising_data(advertising_data)
        if extracted_linkcid != linkcid:
            return {"success": False, "message": "Failed to verify advertising data"}

        return {
            "success": True,
            "message": "LinkCID broadcast completed successfully",
            "linkcid": linkcid,
            "duration_ms": actual_duration,
            "advertising_data_size": len(advertising_data)
        }

    except Exception as e:
        return {"success": False, "message": f"Broadcast failed: {str(e)}"}


def extract_linkcid_from_advertising_data(adv_data):
    """
    Extract LinkCID from BLE advertising data
    Mirrors extract_linkcid_from_mfg_data function from link_protocol.cc

    Args:
        adv_data (bytes): BLE advertising data

    Returns:
        str: Extracted LinkCID or empty string if not found
    """
    MANUFACTURER_ID = 0xFFFF

    # Parse advertising data to find manufacturer data
    i = 0
    while i < len(adv_data):
        if i + 1 >= len(adv_data):
            break

        length = adv_data[i]
        if length == 0 or i + length + 1 > len(adv_data):
            break

        ad_type = adv_data[i + 1]

        # Look for manufacturer data (type 0xFF)
        if ad_type == 0xFF and length >= 3:
            # Extract manufacturer ID (little-endian)
            if i + 4 <= len(adv_data):
                mfg_id = struct.unpack('<H', adv_data[i + 2:i + 4])[0]

                # Check if it's LinkCID manufacturer ID
                if mfg_id == MANUFACTURER_ID:
                    # Extract LinkCID string
                    linkcid_data = adv_data[i + 4:i + 1 + length]
                    return linkcid_data.decode('utf-8', errors='ignore')

        i += length + 1

    return ""


# Test examples
if __name__ == '__main__':
    print("=== LinkCID Broadcast Test ===\n")

    # Test with default LinkCID from link_protocol.cc
    default_linkcid = "QmatnPJPAq8tSDGvrWAGqTjhDyaZMUHPXE8GVGyiTZuYoV"
    print("Test 1: Default LinkCID")
    result = broad_linkcid(default_linkcid)
    print(f"Result: {result}\n")

    # Test with custom LinkCID
    custom_linkcid = "QmTestLinkCID123456789"
    print("Test 2: Custom LinkCID")
    result = broad_linkcid(custom_linkcid)
    print(f"Result: {result}\n")

    # Test with baf-prefixed LinkCID
    baf_linkcid = "bafybeigdyrzt5sfp7udm7hu76uh7y26nf3efuylqabf3oclgtqy55fbzdi"
    print("Test 3: BAF-prefixed LinkCID")
    result = broad_linkcid(baf_linkcid)
    print(f"Result: {result}\n")

    # Test with invalid LinkCID
    print("Test 4: Invalid LinkCID")
    result = broad_linkcid("InvalidLinkCID")
    print(f"Result: {result}\n")

    # Test with empty LinkCID
    print("Test 5: Empty LinkCID")
    result = broad_linkcid("")
    print(f"Result: {result}\n")
