#!/usr/bin/env python3
"""
Test script for LinkCID broadcasting functionality
Based on link_protocol.cc implementation

This script tests the LinkCID broadcast protocol which uses:
- Manufacturer ID: 0xFFFF
- BLE Extended Advertising
- Manufacturer data format: [0xFF, 0xFF, LinkCID_string...]
"""

import struct
import time
import json
import unittest
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, List, Optional, Tuple


class BLEAdvertisingData:
    """Simulates BLE advertising data structure"""
    
    def __init__(self):
        self.flags = 0x06  # General Discoverable + BR/EDR Not Supported
        self.manufacturer_data = b''
        self.device_name = ''
        
    def set_manufacturer_data(self, company_id: int, data: bytes):
        """Set manufacturer specific data"""
        # Little-endian format for company ID
        self.manufacturer_data = struct.pack('<H', company_id) + data
        
    def get_manufacturer_data(self) -> bytes:
        """Get manufacturer data"""
        return self.manufacturer_data
        
    def parse_manufacturer_data(self) -> Tuple[int, bytes]:
        """Parse manufacturer data to get company ID and payload"""
        if len(self.manufacturer_data) < 2:
            return 0, b''
        company_id = struct.unpack('<H', self.manufacturer_data[:2])[0]
        payload = self.manufacturer_data[2:]
        return company_id, payload


class LinkCIDBroadcaster:
    """Simulates the LinkCID broadcasting functionality from link_protocol.cc"""
    
    # Constants from link_protocol.cc
    MANUFACTURER_ID = 0xFFFF  # LinkCID broadcast uses 0xFFFF
    MAX_LINKCID_LENGTH = 1600
    BROADCAST_DURATION_MS = 200
    RANDOM_DELAY_MAX_MS = 20
    
    def __init__(self):
        self.current_broadcast_linkcid = "QmatnPJPAq8tSDGvrWAGqTjhDyaZMUHPXE8GVGyiTZuYoV"
        self.advertising_active = False
        self.advertising_data = BLEAdvertisingData()
        
    def set_broadcast_linkcid(self, new_linkcid: str) -> Dict:
        """Set the LinkCID for broadcasting (mirrors set_broadcast_linkcid function)"""
        if not new_linkcid:
            return {"success": False, "message": "LinkCID cannot be empty"}
            
        self.current_broadcast_linkcid = new_linkcid
        return {
            "success": True,
            "message": "LinkCID updated successfully",
            "linkcid": new_linkcid
        }
        
    def validate_linkcid(self, linkcid: str) -> bool:
        """Validate LinkCID format (mirrors validation logic from link_protocol.cc)"""
        if not linkcid:
            return False
            
        if len(linkcid) > self.MAX_LINKCID_LENGTH:
            return False
            
        # Check for valid IPFS hash format (Qm... or baf...)
        if len(linkcid) >= 10 and (linkcid.startswith("Qm") or linkcid.startswith("baf")):
            return True
            
        return False
        
    def create_advertising_data(self, linkcid: str) -> bytes:
        """Create BLE advertising data with LinkCID (mirrors adv_linkcid function)"""
        if not self.validate_linkcid(linkcid):
            raise ValueError("Invalid LinkCID")
            
        # Create advertising data structure
        self.advertising_data = BLEAdvertisingData()
        
        # Set manufacturer data with LinkCID
        linkcid_bytes = linkcid.encode('utf-8')
        self.advertising_data.set_manufacturer_data(self.MANUFACTURER_ID, linkcid_bytes)
        
        # Build complete advertising packet
        # Flags (3 bytes): Length(1) + Type(1) + Flags(1)
        flags_data = bytes([0x02, 0x01, 0x06])
        
        # Manufacturer data: Length(1) + Type(1) + CompanyID(2) + Data(N)
        mfg_data = self.advertising_data.get_manufacturer_data()
        mfg_header = bytes([len(mfg_data) + 1, 0xFF])  # +1 for type byte
        
        return flags_data + mfg_header + mfg_data
        
    def start_advertising(self, linkcid: str = None) -> bool:
        """Start LinkCID advertising (mirrors adv_linkcid function)"""
        if linkcid is None:
            linkcid = self.current_broadcast_linkcid
            
        try:
            self.create_advertising_data(linkcid)
            self.advertising_active = True
            return True
        except ValueError:
            return False
            
    def stop_advertising(self):
        """Stop advertising"""
        self.advertising_active = False
        
    def extract_linkcid_from_advertising_data(self, adv_data: bytes) -> str:
        """Extract LinkCID from advertising data (mirrors extract_linkcid_from_mfg_data)"""
        # Parse advertising data to find manufacturer data
        i = 0
        while i < len(adv_data):
            if i + 1 >= len(adv_data):
                break
                
            length = adv_data[i]
            if length == 0 or i + length + 1 > len(adv_data):
                break
                
            ad_type = adv_data[i + 1]
            
            # Look for manufacturer data (type 0xFF)
            if ad_type == 0xFF and length >= 3:
                # Extract manufacturer ID (little-endian)
                mfg_id = struct.unpack('<H', adv_data[i + 2:i + 4])[0]
                
                # Check if it's LinkCID manufacturer ID
                if mfg_id == self.MANUFACTURER_ID:
                    # Extract LinkCID string
                    linkcid_data = adv_data[i + 4:i + 1 + length]
                    return linkcid_data.decode('utf-8', errors='ignore')
                    
            i += length + 1
            
        return ""


class TestLinkCIDBroadcast(unittest.TestCase):
    """Test cases for LinkCID broadcasting functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.broadcaster = LinkCIDBroadcaster()
        
    def test_set_broadcast_linkcid_valid(self):
        """Test setting valid LinkCID"""
        test_linkcid = "QmTestLinkCID123456789"
        result = self.broadcaster.set_broadcast_linkcid(test_linkcid)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["linkcid"], test_linkcid)
        self.assertEqual(self.broadcaster.current_broadcast_linkcid, test_linkcid)
        
    def test_set_broadcast_linkcid_empty(self):
        """Test setting empty LinkCID"""
        result = self.broadcaster.set_broadcast_linkcid("")
        
        self.assertFalse(result["success"])
        self.assertIn("cannot be empty", result["message"])
        
    def test_validate_linkcid_valid_qm(self):
        """Test validation of valid Qm-prefixed LinkCID"""
        valid_linkcid = "QmatnPJPAq8tSDGvrWAGqTjhDyaZMUHPXE8GVGyiTZuYoV"
        self.assertTrue(self.broadcaster.validate_linkcid(valid_linkcid))
        
    def test_validate_linkcid_valid_baf(self):
        """Test validation of valid baf-prefixed LinkCID"""
        valid_linkcid = "bafybeigdyrzt5sfp7udm7hu76uh7y26nf3efuylqabf3oclgtqy55fbzdi"
        self.assertTrue(self.broadcaster.validate_linkcid(valid_linkcid))
        
    def test_validate_linkcid_invalid_short(self):
        """Test validation of too short LinkCID"""
        invalid_linkcid = "Qm123"
        self.assertFalse(self.broadcaster.validate_linkcid(invalid_linkcid))
        
    def test_validate_linkcid_invalid_prefix(self):
        """Test validation of invalid prefix"""
        invalid_linkcid = "InvalidPrefixLinkCID123456789"
        self.assertFalse(self.broadcaster.validate_linkcid(invalid_linkcid))
        
    def test_validate_linkcid_too_long(self):
        """Test validation of too long LinkCID"""
        invalid_linkcid = "Qm" + "x" * 1600  # Exceeds MAX_LINKCID_LENGTH
        self.assertFalse(self.broadcaster.validate_linkcid(invalid_linkcid))
        
    def test_create_advertising_data_valid(self):
        """Test creating advertising data with valid LinkCID"""
        test_linkcid = "QmTestLinkCID123456789"
        adv_data = self.broadcaster.create_advertising_data(test_linkcid)
        
        # Verify advertising data structure
        self.assertIsInstance(adv_data, bytes)
        self.assertGreater(len(adv_data), 0)
        
        # Verify flags are present (first 3 bytes)
        self.assertEqual(adv_data[0], 0x02)  # Length
        self.assertEqual(adv_data[1], 0x01)  # Type (Flags)
        self.assertEqual(adv_data[2], 0x06)  # Flags value
        
    def test_create_advertising_data_invalid(self):
        """Test creating advertising data with invalid LinkCID"""
        invalid_linkcid = "InvalidLinkCID"
        
        with self.assertRaises(ValueError):
            self.broadcaster.create_advertising_data(invalid_linkcid)
            
    def test_start_advertising_success(self):
        """Test successful advertising start"""
        test_linkcid = "QmTestLinkCID123456789"
        result = self.broadcaster.start_advertising(test_linkcid)
        
        self.assertTrue(result)
        self.assertTrue(self.broadcaster.advertising_active)
        
    def test_start_advertising_failure(self):
        """Test advertising start with invalid LinkCID"""
        invalid_linkcid = "InvalidLinkCID"
        result = self.broadcaster.start_advertising(invalid_linkcid)
        
        self.assertFalse(result)
        self.assertFalse(self.broadcaster.advertising_active)
        
    def test_stop_advertising(self):
        """Test stopping advertising"""
        test_linkcid = "QmTestLinkCID123456789"
        self.broadcaster.start_advertising(test_linkcid)
        self.assertTrue(self.broadcaster.advertising_active)
        
        self.broadcaster.stop_advertising()
        self.assertFalse(self.broadcaster.advertising_active)
        
    def test_extract_linkcid_from_advertising_data(self):
        """Test extracting LinkCID from advertising data"""
        test_linkcid = "QmTestLinkCID123456789"
        adv_data = self.broadcaster.create_advertising_data(test_linkcid)
        
        extracted_linkcid = self.broadcaster.extract_linkcid_from_advertising_data(adv_data)
        self.assertEqual(extracted_linkcid, test_linkcid)
        
    def test_extract_linkcid_no_manufacturer_data(self):
        """Test extracting LinkCID from advertising data without manufacturer data"""
        # Create advertising data with only flags
        adv_data = bytes([0x02, 0x01, 0x06])
        
        extracted_linkcid = self.broadcaster.extract_linkcid_from_advertising_data(adv_data)
        self.assertEqual(extracted_linkcid, "")
        
    def test_extract_linkcid_wrong_manufacturer_id(self):
        """Test extracting LinkCID with wrong manufacturer ID"""
        # Create advertising data with different manufacturer ID
        flags_data = bytes([0x02, 0x01, 0x06])
        wrong_mfg_data = bytes([0x05, 0xFF, 0x34, 0x12, 0x74, 0x65, 0x73, 0x74])  # Wrong ID: 0x1234
        adv_data = flags_data + wrong_mfg_data
        
        extracted_linkcid = self.broadcaster.extract_linkcid_from_advertising_data(adv_data)
        self.assertEqual(extracted_linkcid, "")
        
    def test_manufacturer_data_format(self):
        """Test manufacturer data format matches protocol specification"""
        test_linkcid = "QmTestLinkCID123456789"
        self.broadcaster.create_advertising_data(test_linkcid)
        
        company_id, payload = self.broadcaster.advertising_data.parse_manufacturer_data()
        
        # Verify manufacturer ID is 0xFFFF
        self.assertEqual(company_id, 0xFFFF)
        
        # Verify payload is the LinkCID
        self.assertEqual(payload.decode('utf-8'), test_linkcid)
        
    def test_default_linkcid(self):
        """Test default LinkCID value"""
        expected_default = "QmatnPJPAq8tSDGvrWAGqTjhDyaZMUHPXE8GVGyiTZuYoV"
        self.assertEqual(self.broadcaster.current_broadcast_linkcid, expected_default)
        
    def test_advertising_with_default_linkcid(self):
        """Test advertising with default LinkCID"""
        result = self.broadcaster.start_advertising()  # No linkcid parameter
        
        self.assertTrue(result)
        self.assertTrue(self.broadcaster.advertising_active)


if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
